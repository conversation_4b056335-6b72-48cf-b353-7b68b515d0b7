{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,gLAAO,EAAC,IAAA,uJAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/ui/accordion.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\"\nimport { ChevronDownIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Accordion({\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Root>) {\n  return <AccordionPrimitive.Root data-slot=\"accordion\" {...props} />\n}\n\nfunction AccordionItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Item>) {\n  return (\n    <AccordionPrimitive.Item\n      data-slot=\"accordion-item\"\n      className={cn(\"border-b last:border-b-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AccordionTrigger({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Trigger>) {\n  return (\n    <AccordionPrimitive.Header className=\"flex\">\n      <AccordionPrimitive.Trigger\n        data-slot=\"accordion-trigger\"\n        className={cn(\n          \"focus-visible:border-ring focus-visible:ring-ring/50 flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none hover:underline focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <ChevronDownIcon className=\"text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200\" />\n      </AccordionPrimitive.Trigger>\n    </AccordionPrimitive.Header>\n  )\n}\n\nfunction AccordionContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Content>) {\n  return (\n    <AccordionPrimitive.Content\n      data-slot=\"accordion-content\"\n      className=\"data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm\"\n      {...props}\n    >\n      <div className={cn(\"pt-0 pb-4\", className)}>{children}</div>\n    </AccordionPrimitive.Content>\n  )\n}\n\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent }\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,UAAU,EACjB,GAAG,OACkD;IACrD,qBAAO,wPAAC,uLAAuB;QAAC,aAAU;QAAa,GAAG,KAAK;;;;;;AACjE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,wPAAC,uLAAuB;QACtB,aAAU;QACV,WAAW,IAAA,mIAAE,EAAC,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD;IACxD,qBACE,wPAAC,yLAAyB;QAAC,WAAU;kBACnC,cAAA,wPAAC,0LAA0B;YACzB,aAAU;YACV,WAAW,IAAA,mIAAE,EACX,8SACA;YAED,GAAG,KAAK;;gBAER;8BACD,wPAAC,qPAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD;IACxD,qBACE,wPAAC,0LAA0B;QACzB,aAAU;QACV,WAAU;QACT,GAAG,KAAK;kBAET,cAAA,wPAAC;YAAI,WAAW,IAAA,mIAAE,EAAC,aAAa;sBAAa;;;;;;;;;;;AAGnD", "debugId": null}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,IAAA,iLAAG,EACxB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,kLAAI,GAAG;IAE9B,qBACE,wPAAC;QACC,aAAU;QACV,WAAW,IAAA,mIAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/ui/navigation-menu.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as NavigationMenuPrimitive from \"@radix-ui/react-navigation-menu\"\nimport { cva } from \"class-variance-authority\"\nimport { ChevronDownIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction NavigationMenu({\n  className,\n  children,\n  viewport = true,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Root> & {\n  viewport?: boolean\n}) {\n  return (\n    <NavigationMenuPrimitive.Root\n      data-slot=\"navigation-menu\"\n      data-viewport={viewport}\n      className={cn(\n        \"group/navigation-menu relative flex max-w-max flex-1 items-center justify-center\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      {viewport && <NavigationMenuViewport />}\n    </NavigationMenuPrimitive.Root>\n  )\n}\n\nfunction NavigationMenuList({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.List>) {\n  return (\n    <NavigationMenuPrimitive.List\n      data-slot=\"navigation-menu-list\"\n      className={cn(\n        \"group flex flex-1 list-none items-center justify-center gap-1\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Item>) {\n  return (\n    <NavigationMenuPrimitive.Item\n      data-slot=\"navigation-menu-item\"\n      className={cn(\"relative\", className)}\n      {...props}\n    />\n  )\n}\n\nconst navigationMenuTriggerStyle = cva(\n  \"group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=open]:hover:bg-accent data-[state=open]:text-accent-foreground data-[state=open]:focus:bg-accent data-[state=open]:bg-accent/50 focus-visible:ring-ring/50 outline-none transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1\"\n)\n\nfunction NavigationMenuTrigger({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Trigger>) {\n  return (\n    <NavigationMenuPrimitive.Trigger\n      data-slot=\"navigation-menu-trigger\"\n      className={cn(navigationMenuTriggerStyle(), \"group\", className)}\n      {...props}\n    >\n      {children}{\" \"}\n      <ChevronDownIcon\n        className=\"relative top-[1px] ml-1 size-3 transition duration-300 group-data-[state=open]:rotate-180\"\n        aria-hidden=\"true\"\n      />\n    </NavigationMenuPrimitive.Trigger>\n  )\n}\n\nfunction NavigationMenuContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Content>) {\n  return (\n    <NavigationMenuPrimitive.Content\n      data-slot=\"navigation-menu-content\"\n      className={cn(\n        \"data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 top-0 left-0 w-full p-2 pr-2.5 md:absolute md:w-auto\",\n        \"group-data-[viewport=false]/navigation-menu:bg-popover group-data-[viewport=false]/navigation-menu:text-popover-foreground group-data-[viewport=false]/navigation-menu:data-[state=open]:animate-in group-data-[viewport=false]/navigation-menu:data-[state=closed]:animate-out group-data-[viewport=false]/navigation-menu:data-[state=closed]:zoom-out-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:zoom-in-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:fade-in-0 group-data-[viewport=false]/navigation-menu:data-[state=closed]:fade-out-0 group-data-[viewport=false]/navigation-menu:top-full group-data-[viewport=false]/navigation-menu:mt-1.5 group-data-[viewport=false]/navigation-menu:overflow-hidden group-data-[viewport=false]/navigation-menu:rounded-md group-data-[viewport=false]/navigation-menu:border group-data-[viewport=false]/navigation-menu:shadow group-data-[viewport=false]/navigation-menu:duration-200 **:data-[slot=navigation-menu-link]:focus:ring-0 **:data-[slot=navigation-menu-link]:focus:outline-none\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuViewport({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Viewport>) {\n  return (\n    <div\n      className={cn(\n        \"absolute top-full left-0 isolate z-50 flex justify-center\"\n      )}\n    >\n      <NavigationMenuPrimitive.Viewport\n        data-slot=\"navigation-menu-viewport\"\n        className={cn(\n          \"origin-top-center bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border shadow md:w-[var(--radix-navigation-menu-viewport-width)]\",\n          className\n        )}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction NavigationMenuLink({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Link>) {\n  return (\n    <NavigationMenuPrimitive.Link\n      data-slot=\"navigation-menu-link\"\n      className={cn(\n        \"data-[active=true]:focus:bg-accent data-[active=true]:hover:bg-accent data-[active=true]:bg-accent/50 data-[active=true]:text-accent-foreground hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus-visible:ring-ring/50 [&_svg:not([class*='text-'])]:text-muted-foreground flex flex-col gap-1 rounded-sm p-2 text-sm transition-all outline-none focus-visible:ring-[3px] focus-visible:outline-1 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuIndicator({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Indicator>) {\n  return (\n    <NavigationMenuPrimitive.Indicator\n      data-slot=\"navigation-menu-indicator\"\n      className={cn(\n        \"data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden\",\n        className\n      )}\n      {...props}\n    >\n      <div className=\"bg-border relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm shadow-md\" />\n    </NavigationMenuPrimitive.Indicator>\n  )\n}\n\nexport {\n  NavigationMenu,\n  NavigationMenuList,\n  NavigationMenuItem,\n  NavigationMenuContent,\n  NavigationMenuTrigger,\n  NavigationMenuLink,\n  NavigationMenuIndicator,\n  NavigationMenuViewport,\n  navigationMenuTriggerStyle,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AAEA;;;;;;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,EACR,WAAW,IAAI,EACf,GAAG,OAGJ;IACC,qBACE,wPAAC,gMAA4B;QAC3B,aAAU;QACV,iBAAe;QACf,WAAW,IAAA,mIAAE,EACX,oFACA;QAED,GAAG,KAAK;;YAER;YACA,0BAAY,wPAAC;;;;;;;;;;;AAGpB;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,wPAAC,gMAA4B;QAC3B,aAAU;QACV,WAAW,IAAA,mIAAE,EACX,iEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,wPAAC,gMAA4B;QAC3B,aAAU;QACV,WAAW,IAAA,mIAAE,EAAC,YAAY;QACzB,GAAG,KAAK;;;;;;AAGf;AAEA,MAAM,6BAA6B,IAAA,iLAAG,EACpC;AAGF,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,wPAAC,mMAA+B;QAC9B,aAAU;QACV,WAAW,IAAA,mIAAE,EAAC,8BAA8B,SAAS;QACpD,GAAG,KAAK;;YAER;YAAU;0BACX,wPAAC,qPAAe;gBACd,WAAU;gBACV,eAAY;;;;;;;;;;;;AAIpB;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,wPAAC,mMAA+B;QAC9B,aAAU;QACV,WAAW,IAAA,mIAAE,EACX,oWACA,6hCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,wPAAC;QACC,WAAW,IAAA,mIAAE,EACX;kBAGF,cAAA,wPAAC,oMAAgC;YAC/B,aAAU;YACV,WAAW,IAAA,mIAAE,EACX,sVACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,wPAAC,gMAA4B;QAC3B,aAAU;QACV,WAAW,IAAA,mIAAE,EACX,ydACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,wBAAwB,EAC/B,SAAS,EACT,GAAG,OAC4D;IAC/D,qBACE,wPAAC,qMAAiC;QAChC,aAAU;QACV,WAAW,IAAA,mIAAE,EACX,gMACA;QAED,GAAG,KAAK;kBAET,cAAA,wPAAC;YAAI,WAAU;;;;;;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 333, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,wPAAC,oLAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,wPAAC,uLAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,wPAAC,qLAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,wPAAC,sLAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,wPAAC,uLAAsB;QACrB,aAAU;QACV,WAAW,IAAA,mIAAE,EACX,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,wPAAC;;0BACC,wPAAC;;;;;0BACD,wPAAC,uLAAsB;gBACrB,aAAU;gBACV,WAAW,IAAA,mIAAE,EACX,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,wPAAC,qLAAoB;wBAAC,WAAU;;0CAC9B,wPAAC,mNAAK;gCAAC,WAAU;;;;;;0CACjB,wPAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,wPAAC;QACC,aAAU;QACV,WAAW,IAAA,mIAAE,EAAC,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,wPAAC;QACC,aAAU;QACV,WAAW,IAAA,mIAAE,EAAC,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,wPAAC,qLAAoB;QACnB,aAAU;QACV,WAAW,IAAA,mIAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,wPAAC,2LAA0B;QACzB,aAAU;QACV,WAAW,IAAA,mIAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 511, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/header/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport Image from 'next/image';\r\nimport { Book, Menu, Sunset, Trees, Zap } from 'lucide-react';\r\nimport {\r\n  Accordion,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from '@/components/ui/accordion';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  NavigationMenu,\r\n  NavigationMenuContent,\r\n  NavigationMenuItem,\r\n  NavigationMenuLink,\r\n  NavigationMenuList,\r\n  NavigationMenuTrigger,\r\n} from '@/components/ui/navigation-menu';\r\nimport {\r\n  Sheet,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetTitle,\r\n  SheetTrigger,\r\n} from '@/components/ui/sheet';\r\n\r\ninterface MenuItem {\r\n  title: string;\r\n  url: string;\r\n  description?: string;\r\n  icon?: React.ReactNode;\r\n  items?: MenuItem[];\r\n}\r\n\r\ninterface HeaderProps {\r\n  logo?: {\r\n    url: string;\r\n    alt: string;\r\n    title: string;\r\n  };\r\n  menu?: MenuItem[];\r\n  auth?: {\r\n    login: {\r\n      title: string;\r\n      url: string;\r\n    };\r\n    signup: {\r\n      title: string;\r\n      url: string;\r\n    };\r\n  };\r\n}\r\n\r\nconst Header = ({\r\n  logo = {\r\n    url: '/',\r\n    alt: 'logo',\r\n    title: 'The Salty Devs',\r\n  },\r\n  menu = [\r\n    { title: 'Articles', url: '/articles' },\r\n    { title: 'Categories', url: '/categories' },\r\n    { title: 'About', url: '/about' },\r\n  ],\r\n  auth = {\r\n    login: { title: 'Login', url: '#' },\r\n    signup: { title: 'Sign up', url: '#' },\r\n  },\r\n}: HeaderProps) => {\r\n  return (\r\n    <section className=\"py-4\">\r\n      <div className=\"container\">\r\n        {/* Desktop Menu */}\r\n        <nav className=\"hidden justify-between lg:flex\">\r\n          <div className=\"flex items-center gap-6\">\r\n            {/* Logo */}\r\n            <a href={logo.url} className=\"flex items-center gap-2\">\r\n              <span className=\"text-lg font-semibold tracking-tighter\">\r\n                {logo.title}\r\n              </span>\r\n            </a>\r\n            <div className=\"flex items-center\">\r\n              <NavigationMenu>\r\n                <NavigationMenuList>\r\n                  {menu.map((item) => renderMenuItem(item))}\r\n                </NavigationMenuList>\r\n              </NavigationMenu>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex gap-2\">\r\n            <Button asChild variant=\"outline\" size=\"sm\">\r\n              <a href={auth.login.url}>{auth.login.title}</a>\r\n            </Button>\r\n            <Button asChild size=\"sm\">\r\n              <a href={auth.signup.url}>{auth.signup.title}</a>\r\n            </Button>\r\n          </div>\r\n        </nav>\r\n\r\n        {/* Mobile Menu */}\r\n        <div className=\"block lg:hidden\">\r\n          <div className=\"flex items-center justify-between\">\r\n            {/* Logo */}\r\n            <a href={logo.url} className=\"flex items-center gap-2\">\r\n              <span className=\"text-lg font-semibold tracking-tighter\">\r\n                {logo.title}\r\n              </span>\r\n            </a>\r\n            <Sheet>\r\n              <SheetTrigger asChild>\r\n                <Button variant=\"outline\" size=\"icon\">\r\n                  <Menu className=\"size-4\" />\r\n                </Button>\r\n              </SheetTrigger>\r\n              <SheetContent className=\"overflow-y-auto\">\r\n                <SheetHeader>\r\n                  <SheetTitle>\r\n                    <a href={logo.url} className=\"flex items-center gap-2\">\r\n                      <span className=\"text-lg font-semibold tracking-tighter\">\r\n                        {logo.title}\r\n                      </span>\r\n                    </a>\r\n                  </SheetTitle>\r\n                </SheetHeader>\r\n                <div className=\"flex flex-col gap-6 p-4\">\r\n                  <Accordion\r\n                    type=\"single\"\r\n                    collapsible\r\n                    className=\"flex w-full flex-col gap-4\"\r\n                  >\r\n                    {menu.map((item) => renderMobileMenuItem(item))}\r\n                  </Accordion>\r\n\r\n                  <div className=\"flex flex-col gap-3\">\r\n                    <Button asChild variant=\"outline\">\r\n                      <a href={auth.login.url}>{auth.login.title}</a>\r\n                    </Button>\r\n                    <Button asChild>\r\n                      <a href={auth.signup.url}>{auth.signup.title}</a>\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              </SheetContent>\r\n            </Sheet>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nconst renderMenuItem = (item: MenuItem) => {\r\n  if (item.items) {\r\n    return (\r\n      <NavigationMenuItem key={item.title}>\r\n        <NavigationMenuTrigger>{item.title}</NavigationMenuTrigger>\r\n        <NavigationMenuContent className=\"bg-popover text-popover-foreground\">\r\n          {item.items.map((subItem) => (\r\n            <NavigationMenuLink asChild key={subItem.title} className=\"w-80\">\r\n              <SubMenuLink item={subItem} />\r\n            </NavigationMenuLink>\r\n          ))}\r\n        </NavigationMenuContent>\r\n      </NavigationMenuItem>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <NavigationMenuItem key={item.title}>\r\n      <NavigationMenuLink\r\n        href={item.url}\r\n        className=\"bg-background hover:bg-muted hover:text-accent-foreground group inline-flex h-10 w-max items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors\"\r\n      >\r\n        {item.title}\r\n      </NavigationMenuLink>\r\n    </NavigationMenuItem>\r\n  );\r\n};\r\n\r\nconst renderMobileMenuItem = (item: MenuItem) => {\r\n  if (item.items) {\r\n    return (\r\n      <AccordionItem key={item.title} value={item.title} className=\"border-b-0\">\r\n        <AccordionTrigger className=\"text-md py-0 font-semibold hover:no-underline\">\r\n          {item.title}\r\n        </AccordionTrigger>\r\n        <AccordionContent className=\"mt-2\">\r\n          {item.items.map((subItem) => (\r\n            <SubMenuLink key={subItem.title} item={subItem} />\r\n          ))}\r\n        </AccordionContent>\r\n      </AccordionItem>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <a key={item.title} href={item.url} className=\"text-md font-semibold\">\r\n      {item.title}\r\n    </a>\r\n  );\r\n};\r\n\r\nconst SubMenuLink = ({ item }: { item: MenuItem }) => {\r\n  return (\r\n    <a\r\n      className=\"hover:bg-muted hover:text-accent-foreground flex select-none flex-row gap-4 rounded-md p-3 leading-none no-underline outline-none transition-colors\"\r\n      href={item.url}\r\n    >\r\n      <div className=\"text-foreground\">{item.icon}</div>\r\n      <div>\r\n        <div className=\"text-sm font-semibold\">{item.title}</div>\r\n        {item.description && (\r\n          <p className=\"text-muted-foreground text-sm leading-snug\">\r\n            {item.description}\r\n          </p>\r\n        )}\r\n      </div>\r\n    </a>\r\n  );\r\n};\r\n\r\nexport default Header;\r\n"], "names": [], "mappings": ";;;;;AAIA;AACA;AAMA;AACA;AAQA;AApBA;;;;;;;AAuDA,MAAM,SAAS,CAAC,EACd,OAAO;IACL,KAAK;IACL,KAAK;IACL,OAAO;AACT,CAAC,EACD,OAAO;IACL;QAAE,OAAO;QAAY,KAAK;IAAY;IACtC;QAAE,OAAO;QAAc,KAAK;IAAc;IAC1C;QAAE,OAAO;QAAS,KAAK;IAAS;CACjC,EACD,OAAO;IACL,OAAO;QAAE,OAAO;QAAS,KAAK;IAAI;IAClC,QAAQ;QAAE,OAAO;QAAW,KAAK;IAAI;AACvC,CAAC,EACW;IACZ,qBACE,wPAAC;QAAQ,WAAU;kBACjB,cAAA,wPAAC;YAAI,WAAU;;8BAEb,wPAAC;oBAAI,WAAU;;sCACb,wPAAC;4BAAI,WAAU;;8CAEb,wPAAC;oCAAE,MAAM,KAAK,GAAG;oCAAE,WAAU;8CAC3B,cAAA,wPAAC;wCAAK,WAAU;kDACb,KAAK,KAAK;;;;;;;;;;;8CAGf,wPAAC;oCAAI,WAAU;8CACb,cAAA,wPAAC,0KAAc;kDACb,cAAA,wPAAC,8KAAkB;sDAChB,KAAK,GAAG,CAAC,CAAC,OAAS,eAAe;;;;;;;;;;;;;;;;;;;;;;sCAK3C,wPAAC;4BAAI,WAAU;;8CACb,wPAAC,sJAAM;oCAAC,OAAO;oCAAC,SAAQ;oCAAU,MAAK;8CACrC,cAAA,wPAAC;wCAAE,MAAM,KAAK,KAAK,CAAC,GAAG;kDAAG,KAAK,KAAK,CAAC,KAAK;;;;;;;;;;;8CAE5C,wPAAC,sJAAM;oCAAC,OAAO;oCAAC,MAAK;8CACnB,cAAA,wPAAC;wCAAE,MAAM,KAAK,MAAM,CAAC,GAAG;kDAAG,KAAK,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;8BAMlD,wPAAC;oBAAI,WAAU;8BACb,cAAA,wPAAC;wBAAI,WAAU;;0CAEb,wPAAC;gCAAE,MAAM,KAAK,GAAG;gCAAE,WAAU;0CAC3B,cAAA,wPAAC;oCAAK,WAAU;8CACb,KAAK,KAAK;;;;;;;;;;;0CAGf,wPAAC,oJAAK;;kDACJ,wPAAC,2JAAY;wCAAC,OAAO;kDACnB,cAAA,wPAAC,sJAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAC7B,cAAA,wPAAC,oNAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAGpB,wPAAC,2JAAY;wCAAC,WAAU;;0DACtB,wPAAC,0JAAW;0DACV,cAAA,wPAAC,yJAAU;8DACT,cAAA,wPAAC;wDAAE,MAAM,KAAK,GAAG;wDAAE,WAAU;kEAC3B,cAAA,wPAAC;4DAAK,WAAU;sEACb,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;0DAKnB,wPAAC;gDAAI,WAAU;;kEACb,wPAAC,4JAAS;wDACR,MAAK;wDACL,WAAW;wDACX,WAAU;kEAET,KAAK,GAAG,CAAC,CAAC,OAAS,qBAAqB;;;;;;kEAG3C,wPAAC;wDAAI,WAAU;;0EACb,wPAAC,sJAAM;gEAAC,OAAO;gEAAC,SAAQ;0EACtB,cAAA,wPAAC;oEAAE,MAAM,KAAK,KAAK,CAAC,GAAG;8EAAG,KAAK,KAAK,CAAC,KAAK;;;;;;;;;;;0EAE5C,wPAAC,sJAAM;gEAAC,OAAO;0EACb,cAAA,wPAAC;oEAAE,MAAM,KAAK,MAAM,CAAC,GAAG;8EAAG,KAAK,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWlE;AAEA,MAAM,iBAAiB,CAAC;IACtB,IAAI,KAAK,KAAK,EAAE;QACd,qBACE,wPAAC,8KAAkB;;8BACjB,wPAAC,iLAAqB;8BAAE,KAAK,KAAK;;;;;;8BAClC,wPAAC,iLAAqB;oBAAC,WAAU;8BAC9B,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,wBACf,wPAAC,8KAAkB;4BAAC,OAAO;4BAAqB,WAAU;sCACxD,cAAA,wPAAC;gCAAY,MAAM;;;;;;2BADY,QAAQ,KAAK;;;;;;;;;;;WAJ3B,KAAK,KAAK;;;;;IAWvC;IAEA,qBACE,wPAAC,8KAAkB;kBACjB,cAAA,wPAAC,8KAAkB;YACjB,MAAM,KAAK,GAAG;YACd,WAAU;sBAET,KAAK,KAAK;;;;;;OALU,KAAK,KAAK;;;;;AASvC;AAEA,MAAM,uBAAuB,CAAC;IAC5B,IAAI,KAAK,KAAK,EAAE;QACd,qBACE,wPAAC,gKAAa;YAAkB,OAAO,KAAK,KAAK;YAAE,WAAU;;8BAC3D,wPAAC,mKAAgB;oBAAC,WAAU;8BACzB,KAAK,KAAK;;;;;;8BAEb,wPAAC,mKAAgB;oBAAC,WAAU;8BACzB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,wBACf,wPAAC;4BAAgC,MAAM;2BAArB,QAAQ,KAAK;;;;;;;;;;;WANjB,KAAK,KAAK;;;;;IAWlC;IAEA,qBACE,wPAAC;QAAmB,MAAM,KAAK,GAAG;QAAE,WAAU;kBAC3C,KAAK,KAAK;OADL,KAAK,KAAK;;;;;AAItB;AAEA,MAAM,cAAc,CAAC,EAAE,IAAI,EAAsB;IAC/C,qBACE,wPAAC;QACC,WAAU;QACV,MAAM,KAAK,GAAG;;0BAEd,wPAAC;gBAAI,WAAU;0BAAmB,KAAK,IAAI;;;;;;0BAC3C,wPAAC;;kCACC,wPAAC;wBAAI,WAAU;kCAAyB,KAAK,KAAK;;;;;;oBACjD,KAAK,WAAW,kBACf,wPAAC;wBAAE,WAAU;kCACV,KAAK,WAAW;;;;;;;;;;;;;;;;;;AAM7B;uCAEe", "debugId": null}}]}