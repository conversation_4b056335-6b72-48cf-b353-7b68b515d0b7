{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 11, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/footer/page.tsx"], "sourcesContent": ["import React from 'react'\r\n\r\nconst Footer = () => {\r\n  return (\r\n    <div className='bg-blue-400'>Footer</div>\r\n  )\r\n}\r\n\r\nexport default Footer"], "names": [], "mappings": ";;;;;;AAEA,MAAM,SAAS;IACb,qBACE,wPAAC;QAAI,WAAU;kBAAc;;;;;;AAEjC;uCAEe", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/app/page.tsx"], "sourcesContent": ["import Footer from \"@/components/footer/page\";\r\nimport Navbar from \"@/components/navbar/page\";\r\n\r\nconst mockPosts = [\r\n  {\r\n    slug: 'type-safe-api-with-prisma',\r\n    title: 'Building a Type-Safe API with Prisma and tRPC',\r\n    date: 'August 18, 2025',\r\n    excerpt: 'Moving beyond traditional REST. How combining Prisma\\'s database safety with tRPC\\'s end-to-end type safety can eliminate entire classes of bugs...',\r\n  },\r\n  {\r\n    slug: 'the-new-wave-of-css',\r\n    title: 'The New Wave of CSS',\r\n    date: 'July 22, 2025',\r\n    excerpt: 'A look at modern CSS features that are changing the way we build layouts and design systems: container queries, the `:has()` selector, and cascade layers.',\r\n  },\r\n  {\r\n    slug: 'server-components-in-nextjs-14',\r\n    title: 'Server Components in Next.js 14: A Practical Guide',\r\n    date: 'June 15, 2025',\r\n    excerpt: 'Moving beyond the theory. A step-by-step guide to refactoring a client-side rendered app to leverage the power of React Server Components for better performance.',\r\n  },\r\n];\r\n\r\nexport default function Home() {\r\n  return (\r\n    <main className=\"h-screen flex flex-col justify-between\">\r\n      <Navbar />\r\n      <Footer />\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;AAGA,MAAM,YAAY;IAChB;QACE,MAAM;QACN,OAAO;QACP,MAAM;QACN,SAAS;IACX;IACA;QACE,MAAM;QACN,OAAO;QACP,MAAM;QACN,SAAS;IACX;IACA;QACE,MAAM;QACN,OAAO;QACP,MAAM;QACN,SAAS;IACX;CACD;AAEc,SAAS;IACtB,qBACE,wPAAC;QAAK,WAAU;;0BACd,wPAAC;;;;;0BACD,wPAAC,yJAAM;;;;;;;;;;;AAGb", "debugId": null}}]}