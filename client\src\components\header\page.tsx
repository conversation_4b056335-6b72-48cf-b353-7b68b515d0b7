'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import DarkModeToggle from './DarkModeToggle/page';

const Header = () => {
  const pathname = usePathname();

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-sm border-b border-border">
      <div className="max-w-6xl mx-auto px-6 h-16 flex items-center justify-between">
        {/* Site Title - Left Side */}
        <Link
          href="/"
          className="font-bold text-xl text-foreground hover:text-primary transition-colors"
        >
          The Salty Devs
        </Link>

        {/* Navigation Links - Right Side */}
        <nav className="flex items-center space-x-6">
          <Link
            href="/articles"
            className={`text-sm font-medium transition-colors ${
              pathname === '/articles'
                ? 'text-primary'
                : 'text-muted-foreground hover:text-foreground'
            }`}
          >
            Articles
          </Link>
          <Link
            href="/categories"
            className={`text-sm font-medium transition-colors ${
              pathname === '/categories'
                ? 'text-primary'
                : 'text-muted-foreground hover:text-foreground'
            }`}
          >
            Categories
          </Link>
          <Link
            href="/about"
            className={`text-sm font-medium transition-colors ${
              pathname === '/about'
                ? 'text-primary'
                : 'text-muted-foreground hover:text-foreground'
            }`}
          >
            About
          </Link>
          <DarkModeToggle />
        </nav>
      </div>
    </header>
  );
};

export default Header;
