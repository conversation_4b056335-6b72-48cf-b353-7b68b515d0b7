var R=require("../chunks/ssr/[turbopack]_runtime.js")("server/app/page.js")
R.c("server/chunks/ssr/1e749_next_dist_f1829e43._.js")
R.c("server/chunks/ssr/[root-of-the-server]__7f148858._.js")
R.c("server/chunks/ssr/client_e618393f._.js")
R.c("server/chunks/ssr/1e749_next_dist_client_components_2e2e2c1d._.js")
R.c("server/chunks/ssr/1e749_next_dist_client_components_builtin_forbidden_e8cbc4f3.js")
R.c("server/chunks/ssr/1e749_next_dist_client_components_builtin_unauthorized_33cde690.js")
R.c("server/chunks/ssr/1e749_next_dist_client_components_builtin_global-error_8690c852.js")
R.c("server/chunks/ssr/1e749_next_dist_12d633eb._.js")
R.c("server/chunks/ssr/[root-of-the-server]__569e2348._.js")
R.m("[project]/client/.next-internal/server/app/page/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/client/node_modules/next/dist/esm/build/templates/app-page.js?page=/page { GLOBAL_ERROR_MODULE => \"[project]/client/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_0 => \"[project]/client/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/client/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/client/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/client/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/client/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/client/src/app/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)")
module.exports=R.m("[project]/client/node_modules/next/dist/esm/build/templates/app-page.js?page=/page { GLOBAL_ERROR_MODULE => \"[project]/client/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_0 => \"[project]/client/src/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/client/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/client/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/client/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/client/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/client/src/app/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)").exports
