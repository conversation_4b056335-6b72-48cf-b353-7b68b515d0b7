{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 11, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/footer/page.tsx"], "sourcesContent": ["import React from 'react'\r\n\r\nconst Footer = () => {\r\n  return (\r\n    <div className='bg-blue-400'>Footer</div>\r\n  )\r\n}\r\n\r\nexport default Footer"], "names": [], "mappings": ";;;;;;AAEA,MAAM,SAAS;IACb,qBACE,wPAAC;QAAI,WAAU;kBAAc;;;;;;AAEjC;uCAEe", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/header/DarkModeToggle/page.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/client/src/components/header/DarkModeToggle/page.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/header/DarkModeToggle/page.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,kRAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAwT,GACrV,sFACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/header/DarkModeToggle/page.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/client/src/components/header/DarkModeToggle/page.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/header/DarkModeToggle/page.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,kRAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/header/page.tsx"], "sourcesContent": ["import React from 'react';\r\nimport Link from 'next/link';\r\nimport { usePathname } from 'next/navigation';\r\nimport DarkModeToggle from '@/components/header/DarkModeToggle/page';\r\n\r\nconst Header = () => {\r\n  const pathname = usePathname();\r\n\r\n  const navigation = [\r\n    { name: 'About', href: '/about' },\r\n    { name: 'Services', href: '/services' },\r\n    { name: 'Contact', href: '/contact' },\r\n  ];\r\n\r\n  return (\r\n    <header className=\"fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-sm border-b border-border\">\r\n      <div className=\"max-w-6xl mx-auto px-6 h-16 flex items-center justify-between\">\r\n        {/* Logo */}\r\n        <Link\r\n          href=\"/\"\r\n          className=\"font-bold text-xl text-foreground hover:gradient-text\"\r\n        >\r\n          The Salty Devs\r\n        </Link>\r\n\r\n        {/* Navigation */}\r\n        <nav className=\"flex items-center space-x-8\">\r\n          {navigation.map((item) => (\r\n            <Link\r\n              key={item.name}\r\n              href={item.href}\r\n              className={`text-sm font-medium transition-colors ${\r\n                pathname === item.href\r\n                  ? 'gradient-text'\r\n                  : 'text-muted-foreground hover:text-foreground'\r\n              }`}\r\n            >\r\n              {item.name}\r\n            </Link>\r\n          ))}\r\n          <DarkModeToggle />\r\n        </nav>\r\n      </div>\r\n    </header>\r\n  );\r\n};\r\n\r\nexport default Header;\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAAA;AACA;;;;;AAEA,MAAM,SAAS;IACb,MAAM,WAAW,IAAA,8MAAW;IAE5B,MAAM,aAAa;QACjB;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,qBACE,wPAAC;QAAO,WAAU;kBAChB,cAAA,wPAAC;YAAI,WAAU;;8BAEb,wPAAC,iLAAI;oBACH,MAAK;oBACL,WAAU;8BACX;;;;;;8BAKD,wPAAC;oBAAI,WAAU;;wBACZ,WAAW,GAAG,CAAC,CAAC,qBACf,wPAAC,iLAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC,sCAAsC,EAChD,aAAa,KAAK,IAAI,GAClB,kBACA,+CACJ;0CAED,KAAK,IAAI;+BARL,KAAK,IAAI;;;;;sCAWlB,wPAAC,2KAAc;;;;;;;;;;;;;;;;;;;;;;AAKzB;uCAEe", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/app/page.tsx"], "sourcesContent": ["import Footer from '@/components/footer/page';\r\nimport Header from '@/components/header/page';\r\n\r\nconst mockPosts = [\r\n  {\r\n    slug: 'type-safe-api-with-prisma',\r\n    title: 'Building a Type-Safe API with Prisma and tRPC',\r\n    date: 'August 18, 2025',\r\n    excerpt:\r\n      \"Moving beyond traditional REST. How combining Prisma's database safety with tRPC's end-to-end type safety can eliminate entire classes of bugs...\",\r\n  },\r\n  {\r\n    slug: 'the-new-wave-of-css',\r\n    title: 'The New Wave of CSS',\r\n    date: 'July 22, 2025',\r\n    excerpt:\r\n      'A look at modern CSS features that are changing the way we build layouts and design systems: container queries, the `:has()` selector, and cascade layers.',\r\n  },\r\n  {\r\n    slug: 'server-components-in-nextjs-14',\r\n    title: 'Server Components in Next.js 14: A Practical Guide',\r\n    date: 'June 15, 2025',\r\n    excerpt:\r\n      'Moving beyond the theory. A step-by-step guide to refactoring a client-side rendered app to leverage the power of React Server Components for better performance.',\r\n  },\r\n];\r\n\r\nexport default function Home() {\r\n  return (\r\n    <main className=\"h-screen flex flex-col justify-between\">\r\n      <Header />\r\n      <Footer />\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEA,MAAM,YAAY;IAChB;QACE,MAAM;QACN,OAAO;QACP,MAAM;QACN,SACE;IACJ;IACA;QACE,MAAM;QACN,OAAO;QACP,MAAM;QACN,SACE;IACJ;IACA;QACE,MAAM;QACN,OAAO;QACP,MAAM;QACN,SACE;IACJ;CACD;AAEc,SAAS;IACtB,qBACE,wPAAC;QAAK,WAAU;;0BACd,wPAAC,yJAAM;;;;;0BACP,wPAAC,yJAAM;;;;;;;;;;;AAGb", "debugId": null}}]}