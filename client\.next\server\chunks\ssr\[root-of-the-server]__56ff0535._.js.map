{"version": 3, "sources": [], "sections": [{"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/header/DarkModeToggle/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useTheme } from 'next-themes';\r\nimport { Moon, Sun } from 'lucide-react';\r\n\r\nconst DarkModeToggle = () => {\r\n  const { theme, setTheme } = useTheme();\r\n  const [mounted, setMounted] = useState(false);\r\n\r\n  // Avoid hydration mismatch by only rendering after mount\r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  if (!mounted) {\r\n    // Return a placeholder with the same dimensions to avoid layout shift\r\n    return (\r\n      <button className=\"w-9 h-9 rounded-md border border-transparent\">\r\n        <span className=\"sr-only\">Loading theme toggle</span>\r\n      </button>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <button\r\n      onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}\r\n      className=\"w-9 h-9 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center justify-center\"\r\n      aria-label=\"Toggle theme\"\r\n    >\r\n      {theme === 'dark' ? (\r\n        <Sun className=\"h-4 w-4 text-yellow-500\" />\r\n      ) : (\r\n        <Moon className=\"h-4 w-4 text-gray-700 dark:text-gray-300\" />\r\n      )}\r\n    </button>\r\n  );\r\n};\r\n\r\nexport default DarkModeToggle;\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAMA,MAAM,iBAAiB;IACrB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,IAAA,sKAAQ;IACpC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,2NAAQ,EAAC;IAEvC,yDAAyD;IACzD,IAAA,4NAAS,EAAC;QACR,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,sEAAsE;QACtE,qBACE,wPAAC;YAAO,WAAU;sBAChB,cAAA,wPAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;IAGhC;IAEA,qBACE,wPAAC;QACC,SAAS,IAAM,SAAS,UAAU,SAAS,UAAU;QACrD,WAAU;QACV,cAAW;kBAEV,UAAU,uBACT,wPAAC,iNAAG;YAAC,WAAU;;;;;qEAEf,wPAAC,oNAAI;YAAC,WAAU;;;;;;;;;;;AAIxB;uCAEe", "debugId": null}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/header/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport { usePathname } from 'next/navigation';\r\nimport DarkModeToggle from './DarkModeToggle/page';\r\n\r\nconst Header = () => {\r\n  const pathname = usePathname();\r\n\r\n  return (\r\n    <header className=\"fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-sm border-b border-border\">\r\n      <div className=\"max-w-6xl mx-auto px-6 h-16 flex items-center justify-between\">\r\n        {/* Site Title - Left Side */}\r\n        <Link\r\n          href=\"/\"\r\n          className=\"font-bold text-xl text-foreground hover:text-primary transition-colors\"\r\n        >\r\n          The Salty Devs\r\n        </Link>\r\n\r\n        {/* Navigation Links - Right Side */}\r\n        <nav className=\"flex items-center space-x-6\">\r\n          <Link\r\n            href=\"/articles\"\r\n            className={`text-sm font-medium transition-colors ${\r\n              pathname === '/articles'\r\n                ? 'text-primary'\r\n                : 'text-muted-foreground hover:text-foreground'\r\n            }`}\r\n          >\r\n            Articles\r\n          </Link>\r\n          <Link\r\n            href=\"/categories\"\r\n            className={`text-sm font-medium transition-colors ${\r\n              pathname === '/categories'\r\n                ? 'text-primary'\r\n                : 'text-muted-foreground hover:text-foreground'\r\n            }`}\r\n          >\r\n            Categories\r\n          </Link>\r\n          <Link\r\n            href=\"/about\"\r\n            className={`text-sm font-medium transition-colors ${\r\n              pathname === '/about'\r\n                ? 'text-primary'\r\n                : 'text-muted-foreground hover:text-foreground'\r\n            }`}\r\n          >\r\n            About\r\n          </Link>\r\n          <DarkModeToggle />\r\n        </nav>\r\n      </div>\r\n    </header>\r\n  );\r\n};\r\n\r\nexport default Header;\r\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AACA;AALA;;;;;AAOA,MAAM,SAAS;IACb,MAAM,WAAW,IAAA,2JAAW;IAE5B,qBACE,wPAAC;QAAO,WAAU;kBAChB,cAAA,wPAAC;YAAI,WAAU;;8BAEb,wPAAC,iLAAI;oBACH,MAAK;oBACL,WAAU;8BACX;;;;;;8BAKD,wPAAC;oBAAI,WAAU;;sCACb,wPAAC,iLAAI;4BACH,MAAK;4BACL,WAAW,CAAC,sCAAsC,EAChD,aAAa,cACT,iBACA,+CACJ;sCACH;;;;;;sCAGD,wPAAC,iLAAI;4BACH,MAAK;4BACL,WAAW,CAAC,sCAAsC,EAChD,aAAa,gBACT,iBACA,+CACJ;sCACH;;;;;;sCAGD,wPAAC,iLAAI;4BACH,MAAK;4BACL,WAAW,CAAC,sCAAsC,EAChD,aAAa,WACT,iBACA,+CACJ;sCACH;;;;;;sCAGD,wPAAC,2KAAc;;;;;;;;;;;;;;;;;;;;;;AAKzB;uCAEe", "debugId": null}}]}