var R=require("../chunks/ssr/[turbopack]_runtime.js")("server/pages/_error.js")
R.c("server/chunks/ssr/1e749_70e684c9._.js")
R.c("server/chunks/ssr/[root-of-the-server]__e6a4d965._.js")
R.c("server/chunks/ssr/1e749_next_32c43e2e._.js")
R.c("server/chunks/ssr/1e749_18565bd1._.js")
R.c("server/chunks/ssr/[externals]_next_dist_shared_lib_no-fallback-error_external_59b92b38.js")
R.m("[project]/client/node_modules/next/dist/esm/build/templates/pages.js { INNER_PAGE => \"[project]/client/node_modules/next/error.js [ssr] (ecmascript)\", INNER_DOCUMENT => \"[project]/client/node_modules/next/document.js [ssr] (ecmascript)\", INNER_APP => \"[project]/client/node_modules/next/app.js [ssr] (ecmascript)\" } [ssr] (ecmascript)")
module.exports=R.m("[project]/client/node_modules/next/dist/esm/build/templates/pages.js { INNER_PAGE => \"[project]/client/node_modules/next/error.js [ssr] (ecmascript)\", INNER_DOCUMENT => \"[project]/client/node_modules/next/document.js [ssr] (ecmascript)\", INNER_APP => \"[project]/client/node_modules/next/app.js [ssr] (ecmascript)\" } [ssr] (ecmascript)").exports
