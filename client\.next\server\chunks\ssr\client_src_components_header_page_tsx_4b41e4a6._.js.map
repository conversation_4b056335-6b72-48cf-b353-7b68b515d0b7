{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/header/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport { usePathname } from 'next/navigation';\r\nimport DarkModeToggle from '/DarkModeToggle/page';\r\n\r\nconst Header = () => {\r\n  const pathname = usePathname();\r\n\r\n  const navigation = [\r\n    { name: 'About', href: '/about' },\r\n    { name: 'Services', href: '/services' },\r\n    { name: 'Contact', href: '/contact' },\r\n  ];\r\n\r\n  return (\r\n    <header className=\"fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-sm border-b border-border\">\r\n      <div className=\"max-w-6xl mx-auto px-6 h-16 flex items-center justify-between\">\r\n        {/* Logo */}\r\n        <Link\r\n          href=\"/\"\r\n          className=\"font-bold text-xl text-foreground hover:gradient-text\"\r\n        >\r\n          The Salty Devs\r\n        </Link>\r\n\r\n        {/* Navigation */}\r\n        <nav className=\"flex items-center space-x-8\">\r\n          {navigation.map((item) => (\r\n            <Link\r\n              key={item.name}\r\n              href={item.href}\r\n              className={`text-sm font-medium transition-colors ${\r\n                pathname === item.href\r\n                  ? 'gradient-text'\r\n                  : 'text-muted-foreground hover:text-foreground'\r\n              }`}\r\n            >\r\n              {item.name}\r\n            </Link>\r\n          ))}\r\n          <DarkModeToggle />\r\n        </nav>\r\n      </div>\r\n    </header>\r\n  );\r\n};\r\n\r\nexport default Header;\r\n"], "names": [], "mappings": ";;;;;AAGA;AACA;;;;;;AAJA;;;;;AAOA,MAAM,SAAS;IACb,MAAM,WAAW,IAAA,2JAAW;IAE5B,MAAM,aAAa;QACjB;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,qBACE,wPAAC;QAAO,WAAU;kBAChB,cAAA,wPAAC;YAAI,WAAU;;8BAEb,wPAAC,iLAAI;oBACH,MAAK;oBACL,WAAU;8BACX;;;;;;8BAKD,wPAAC;oBAAI,WAAU;;wBACZ,WAAW,GAAG,CAAC,CAAC,qBACf,wPAAC,iLAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC,sCAAsC,EAChD,aAAa,KAAK,IAAI,GAClB,kBACA,+CACJ;0CAED,KAAK,IAAI;+BARL,KAAK,IAAI;;;;;sCAWlB,wPAAC;;;;;;;;;;;;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}]}