{"name": "server", "version": "1.0.0", "description": "Server for 'The Salty Devs' Blog app", "main": "src/server.ts", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "npx tsc", "start": "node src/server.ts", "dev": "nodemon --watch src --ext ts --exec ts-node-esm src/server.ts", "prisma:generate": "npx prisma generate"}, "repository": {"type": "git", "url": "git+https://github.com/anshoolp-endure/The-Salty-Devs.git"}, "author": "<PERSON><PERSON><PERSON> and Rhythm Naik", "license": "ISC", "bugs": {"url": "https://github.com/anshoolp-endure/The-Salty-Devs/issues"}, "homepage": "https://github.com/anshoolp-endure/The-Salty-Devs#readme", "dependencies": {"@prisma/client": "^6.15.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-validator": "^7.2.1", "the-salty-devs": "file:.."}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/cors": "^2.8.19", "@types/dotenv": "^8.2.3", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.3.0", "esbuild-register": "^3.6.0", "nodemon": "^3.1.10", "prisma": "^6.15.0", "ts-node": "^10.9.2", "typescript": "^5.9.2"}}