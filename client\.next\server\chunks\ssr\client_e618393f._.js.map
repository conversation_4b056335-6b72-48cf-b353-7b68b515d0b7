{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from 'next';\r\nimport { ThemeProvider } from 'next-themes';\r\nimport '../styles/globals.css';\r\n\r\nexport const metadata: Metadata = {\r\n  title: 'The Salty Devs',\r\n  description: 'The Salty Devs Blog Page',\r\n};\r\n\r\nexport default function RootLayout({\r\n  children,\r\n}: Readonly<{\r\n  children: React.ReactNode;\r\n}>) {\r\n  return (\r\n    <html lang=\"en\" suppressHydrationWarning={true}>\r\n      <body suppressHydrationWarning>\r\n        <ThemeProvider\r\n          attribute=\"class\"\r\n          defaultTheme=\"system\"\r\n          enableSystem\r\n          disableTransitionOnChange\r\n        >\r\n          {children}\r\n        </ThemeProvider>\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AACA;;;;AAGO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,wPAAC;QAAK,MAAK;QAAK,0BAA0B;kBACxC,cAAA,wPAAC;YAAK,wBAAwB;sBAC5B,cAAA,wPAAC,2KAAa;gBACZ,WAAU;gBACV,cAAa;gBACb,YAAY;gBACZ,yBAAyB;0BAExB;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,mIACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/node_modules/next-themes/dist/index.mjs/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/node_modules/next-themes/dist/index.mjs <module evaluation>\",\n    \"ThemeProvider\",\n);\nexport const useTheme = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/node_modules/next-themes/dist/index.mjs <module evaluation>\",\n    \"useTheme\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;AACvE;;AACO,MAAM,gBAAgB,IAAA,kRAAuB,EAChD;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,gFACA;AAEG,MAAM,WAAW,IAAA,kRAAuB,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,gFACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/node_modules/next-themes/dist/index.mjs/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/node_modules/next-themes/dist/index.mjs\",\n    \"ThemeProvider\",\n);\nexport const useTheme = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/node_modules/next-themes/dist/index.mjs\",\n    \"useTheme\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;AACvE;;AACO,MAAM,gBAAgB,IAAA,kRAAuB,EAChD;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,4DACA;AAEG,MAAM,WAAW,IAAA,kRAAuB,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,4DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}