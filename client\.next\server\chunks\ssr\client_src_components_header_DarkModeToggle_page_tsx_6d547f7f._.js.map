{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/header/DarkModeToggle/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useTheme } from 'next-themes';\r\nimport { Moon, Sun } from 'lucide-react';\r\n\r\nconst DarkModeToggle = () => {\r\n  const { theme, setTheme } = useTheme();\r\n  const [mounted, setMounted] = useState(false);\r\n\r\n  // Avoid hydration mismatch by only rendering after mount\r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  if (!mounted) {\r\n    // Return a placeholder with the same dimensions to avoid layout shift\r\n    return (\r\n      <button className=\"w-9 h-9 rounded-md border border-transparent\">\r\n        <span className=\"sr-only\">Loading theme toggle</span>\r\n      </button>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <button\r\n      onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}\r\n      className=\"w-9 h-9 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center justify-center\"\r\n      aria-label=\"Toggle theme\"\r\n    >\r\n      {theme === 'dark' ? (\r\n        <Sun className=\"h-4 w-4 text-yellow-500\" />\r\n      ) : (\r\n        <Moon className=\"h-4 w-4 text-gray-700 dark:text-gray-300\" />\r\n      )}\r\n    </button>\r\n  );\r\n};\r\n\r\nexport default DarkModeToggle;\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAMA,MAAM,iBAAiB;IACrB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,IAAA,sKAAQ;IACpC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,2NAAQ,EAAC;IAEvC,yDAAyD;IACzD,IAAA,4NAAS,EAAC;QACR,WAAW;IACb,GAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,sEAAsE;QACtE,qBACE,wPAAC;YAAO,WAAU;sBAChB,cAAA,wPAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;IAGhC;IAEA,qBACE,wPAAC;QACC,SAAS,IAAM,SAAS,UAAU,SAAS,UAAU;QACrD,WAAU;QACV,cAAW;kBAEV,UAAU,uBACT,wPAAC,uMAAG;YAAC,WAAU;;;;;qEAEf,wPAAC,0MAAI;YAAC,WAAU;;;;;;;;;;;AAIxB;uCAEe", "debugId": null}}]}