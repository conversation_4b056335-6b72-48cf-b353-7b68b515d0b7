{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/header/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport { usePathname } from 'next/navigation';\r\nimport DarkModeToggle from './DarkModeToggle/page';\r\n\r\ninterface MenuItem {\r\n  title: string;\r\n  url: string;\r\n  description?: string;\r\n  icon?: React.ReactNode;\r\n  items?: MenuItem[];\r\n}\r\n\r\ninterface HeaderProps {\r\n  logo?: {\r\n    url: string;\r\n    src: string;\r\n    alt: string;\r\n    title: string;\r\n  };\r\n  menu?: MenuItem[];\r\n  auth?: {\r\n    login: {\r\n      title: string;\r\n      url: string;\r\n    };\r\n    signup: {\r\n      title: string;\r\n      url: string;\r\n    };\r\n  };\r\n}\r\n\r\nconst Header = ({\r\n  logo = {\r\n    url: 'https://www.shadcnblocks.com',\r\n    src: 'https://deifkwefumgah.cloudfront.net/shadcnblocks/block/logos/shadcnblockscom-icon.svg',\r\n    alt: 'logo',\r\n    title: 'Shadcnblocks.com',\r\n  },\r\n  menu = [\r\n    { title: 'Home', url: '#' },\r\n    {\r\n      title: 'Products',\r\n      url: '#',\r\n      items: [\r\n        {\r\n          title: 'Blog',\r\n          description: 'The latest industry news, updates, and info',\r\n          icon: <Book className=\"size-5 shrink-0\" />,\r\n          url: '#',\r\n        },\r\n        {\r\n          title: 'Company',\r\n          description: 'Our mission is to innovate and empower the world',\r\n          icon: <Trees className=\"size-5 shrink-0\" />,\r\n          url: '#',\r\n        },\r\n        {\r\n          title: 'Careers',\r\n          description: 'Browse job listing and discover our workspace',\r\n          icon: <Sunset className=\"size-5 shrink-0\" />,\r\n          url: '#',\r\n        },\r\n        {\r\n          title: 'Support',\r\n          description:\r\n            'Get in touch with our support team or visit our community forums',\r\n          icon: <Zap className=\"size-5 shrink-0\" />,\r\n          url: '#',\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: 'Resources',\r\n      url: '#',\r\n      items: [\r\n        {\r\n          title: 'Help Center',\r\n          description: 'Get all the answers you need right here',\r\n          icon: <Zap className=\"size-5 shrink-0\" />,\r\n          url: '#',\r\n        },\r\n        {\r\n          title: 'Contact Us',\r\n          description: 'We are here to help you with any questions you have',\r\n          icon: <Sunset className=\"size-5 shrink-0\" />,\r\n          url: '#',\r\n        },\r\n        {\r\n          title: 'Status',\r\n          description: 'Check the current status of our services and APIs',\r\n          icon: <Trees className=\"size-5 shrink-0\" />,\r\n          url: '#',\r\n        },\r\n        {\r\n          title: 'Terms of Service',\r\n          description: 'Our terms and conditions for using our services',\r\n          icon: <Book className=\"size-5 shrink-0\" />,\r\n          url: '#',\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: 'Pricing',\r\n      url: '#',\r\n    },\r\n    {\r\n      title: 'Blog',\r\n      url: '#',\r\n    },\r\n  ],\r\n  auth = {\r\n    login: { title: 'Login', url: '#' },\r\n    signup: { title: 'Sign up', url: '#' },\r\n  },\r\n}: HeaderProps) => {\r\n  return (\r\n    <section className=\"py-4\">\r\n      <div className=\"container\">\r\n        {/* Desktop Menu */}\r\n        <nav className=\"hidden justify-between lg:flex\">\r\n          <div className=\"flex items-center gap-6\">\r\n            {/* Logo */}\r\n            <a href={logo.url} className=\"flex items-center gap-2\">\r\n              <Image\r\n                src={logo.src}\r\n                className=\"max-h-8 dark:invert\"\r\n                alt={logo.alt}\r\n              />\r\n              <span className=\"text-lg font-semibold tracking-tighter\">\r\n                {logo.title}\r\n              </span>\r\n            </a>\r\n            <div className=\"flex items-center\">\r\n              <NavigationMenu>\r\n                <NavigationMenuList>\r\n                  {menu.map((item) => renderMenuItem(item))}\r\n                </NavigationMenuList>\r\n              </NavigationMenu>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex gap-2\">\r\n            <Button asChild variant=\"outline\" size=\"sm\">\r\n              <a href={auth.login.url}>{auth.login.title}</a>\r\n            </Button>\r\n            <Button asChild size=\"sm\">\r\n              <a href={auth.signup.url}>{auth.signup.title}</a>\r\n            </Button>\r\n          </div>\r\n        </nav>\r\n\r\n        {/* Mobile Menu */}\r\n        <div className=\"block lg:hidden\">\r\n          <div className=\"flex items-center justify-between\">\r\n            {/* Logo */}\r\n            <a href={logo.url} className=\"flex items-center gap-2\">\r\n              <Image\r\n                src={logo.src}\r\n                className=\"max-h-8 dark:invert\"\r\n                alt={logo.alt}\r\n              />\r\n            </a>\r\n            <Sheet>\r\n              <SheetTrigger asChild>\r\n                <Button variant=\"outline\" size=\"icon\">\r\n                  <Menu className=\"size-4\" />\r\n                </Button>\r\n              </SheetTrigger>\r\n              <SheetContent className=\"overflow-y-auto\">\r\n                <SheetHeader>\r\n                  <SheetTitle>\r\n                    <a href={logo.url} className=\"flex items-center gap-2\">\r\n                      <Image\r\n                        src={logo.src}\r\n                        className=\"max-h-8 dark:invert\"\r\n                        alt={logo.alt}\r\n                      />\r\n                    </a>\r\n                  </SheetTitle>\r\n                </SheetHeader>\r\n                <div className=\"flex flex-col gap-6 p-4\">\r\n                  <Accordion\r\n                    type=\"single\"\r\n                    collapsible\r\n                    className=\"flex w-full flex-col gap-4\"\r\n                  >\r\n                    {menu.map((item) => renderMobileMenuItem(item))}\r\n                  </Accordion>\r\n\r\n                  <div className=\"flex flex-col gap-3\">\r\n                    <Button asChild variant=\"outline\">\r\n                      <a href={auth.login.url}>{auth.login.title}</a>\r\n                    </Button>\r\n                    <Button asChild>\r\n                      <a href={auth.signup.url}>{auth.signup.title}</a>\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              </SheetContent>\r\n            </Sheet>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nconst renderMenuItem = (item: MenuItem) => {\r\n  if (item.items) {\r\n    return (\r\n      <NavigationMenuItem key={item.title}>\r\n        <NavigationMenuTrigger>{item.title}</NavigationMenuTrigger>\r\n        <NavigationMenuContent className=\"bg-popover text-popover-foreground\">\r\n          {item.items.map((subItem) => (\r\n            <NavigationMenuLink asChild key={subItem.title} className=\"w-80\">\r\n              <SubMenuLink item={subItem} />\r\n            </NavigationMenuLink>\r\n          ))}\r\n        </NavigationMenuContent>\r\n      </NavigationMenuItem>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <NavigationMenuItem key={item.title}>\r\n      <NavigationMenuLink\r\n        href={item.url}\r\n        className=\"bg-background hover:bg-muted hover:text-accent-foreground group inline-flex h-10 w-max items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors\"\r\n      >\r\n        {item.title}\r\n      </NavigationMenuLink>\r\n    </NavigationMenuItem>\r\n  );\r\n};\r\n\r\nconst renderMobileMenuItem = (item: MenuItem) => {\r\n  if (item.items) {\r\n    return (\r\n      <AccordionItem key={item.title} value={item.title} className=\"border-b-0\">\r\n        <AccordionTrigger className=\"text-md py-0 font-semibold hover:no-underline\">\r\n          {item.title}\r\n        </AccordionTrigger>\r\n        <AccordionContent className=\"mt-2\">\r\n          {item.items.map((subItem) => (\r\n            <SubMenuLink key={subItem.title} item={subItem} />\r\n          ))}\r\n        </AccordionContent>\r\n      </AccordionItem>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <a key={item.title} href={item.url} className=\"text-md font-semibold\">\r\n      {item.title}\r\n    </a>\r\n  );\r\n};\r\n\r\nconst SubMenuLink = ({ item }: { item: MenuItem }) => {\r\n  return (\r\n    <a\r\n      className=\"hover:bg-muted hover:text-accent-foreground flex select-none flex-row gap-4 rounded-md p-3 leading-none no-underline outline-none transition-colors\"\r\n      href={item.url}\r\n    >\r\n      <div className=\"text-foreground\">{item.icon}</div>\r\n      <div>\r\n        <div className=\"text-sm font-semibold\">{item.title}</div>\r\n        {item.description && (\r\n          <p className=\"text-muted-foreground text-sm leading-snug\">\r\n            {item.description}\r\n          </p>\r\n        )}\r\n      </div>\r\n    </a>\r\n  );\r\n};\r\n\r\nexport { Header };\r\n"], "names": [], "mappings": ";;;;;AAAA;;AAmCA,MAAM,SAAS;QAAC,EACd,OAAO;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,OAAO;IACT,CAAC,EACD,OAAO;QACL;YAAE,OAAO;YAAQ,KAAK;QAAI;QAC1B;YACE,OAAO;YACP,KAAK;YACL,OAAO;gBACL;oBACE,OAAO;oBACP,aAAa;oBACb,oBAAM,uMAAC;wBAAK,WAAU;;;;;;oBACtB,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,aAAa;oBACb,oBAAM,uMAAC;wBAAM,WAAU;;;;;;oBACvB,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,aAAa;oBACb,oBAAM,uMAAC;wBAAO,WAAU;;;;;;oBACxB,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,aACE;oBACF,oBAAM,uMAAC;wBAAI,WAAU;;;;;;oBACrB,KAAK;gBACP;aACD;QACH;QACA;YACE,OAAO;YACP,KAAK;YACL,OAAO;gBACL;oBACE,OAAO;oBACP,aAAa;oBACb,oBAAM,uMAAC;wBAAI,WAAU;;;;;;oBACrB,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,aAAa;oBACb,oBAAM,uMAAC;wBAAO,WAAU;;;;;;oBACxB,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,aAAa;oBACb,oBAAM,uMAAC;wBAAM,WAAU;;;;;;oBACvB,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,aAAa;oBACb,oBAAM,uMAAC;wBAAK,WAAU;;;;;;oBACtB,KAAK;gBACP;aACD;QACH;QACA;YACE,OAAO;YACP,KAAK;QACP;QACA;YACE,OAAO;YACP,KAAK;QACP;KACD,EACD,OAAO;QACL,OAAO;YAAE,OAAO;YAAS,KAAK;QAAI;QAClC,QAAQ;YAAE,OAAO;YAAW,KAAK;QAAI;IACvC,CAAC,EACW;IACZ,qBACE,uMAAC;QAAQ,WAAU;kBACjB,cAAA,uMAAC;YAAI,WAAU;;8BAEb,uMAAC;oBAAI,WAAU;;sCACb,uMAAC;4BAAI,WAAU;;8CAEb,uMAAC;oCAAE,MAAM,KAAK,GAAG;oCAAE,WAAU;;sDAC3B,uMAAC;4CACC,KAAK,KAAK,GAAG;4CACb,WAAU;4CACV,KAAK,KAAK,GAAG;;;;;;sDAEf,uMAAC;4CAAK,WAAU;sDACb,KAAK,KAAK;;;;;;;;;;;;8CAGf,uMAAC;oCAAI,WAAU;8CACb,cAAA,uMAAC;kDACC,cAAA,uMAAC;sDACE,KAAK,GAAG,CAAC,CAAC,OAAS,eAAe;;;;;;;;;;;;;;;;;;;;;;sCAK3C,uMAAC;4BAAI,WAAU;;8CACb,uMAAC;oCAAO,OAAO;oCAAC,SAAQ;oCAAU,MAAK;8CACrC,cAAA,uMAAC;wCAAE,MAAM,KAAK,KAAK,CAAC,GAAG;kDAAG,KAAK,KAAK,CAAC,KAAK;;;;;;;;;;;8CAE5C,uMAAC;oCAAO,OAAO;oCAAC,MAAK;8CACnB,cAAA,uMAAC;wCAAE,MAAM,KAAK,MAAM,CAAC,GAAG;kDAAG,KAAK,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;8BAMlD,uMAAC;oBAAI,WAAU;8BACb,cAAA,uMAAC;wBAAI,WAAU;;0CAEb,uMAAC;gCAAE,MAAM,KAAK,GAAG;gCAAE,WAAU;0CAC3B,cAAA,uMAAC;oCACC,KAAK,KAAK,GAAG;oCACb,WAAU;oCACV,KAAK,KAAK,GAAG;;;;;;;;;;;0CAGjB,uMAAC;;kDACC,uMAAC;wCAAa,OAAO;kDACnB,cAAA,uMAAC;4CAAO,SAAQ;4CAAU,MAAK;sDAC7B,cAAA,uMAAC;gDAAK,WAAU;;;;;;;;;;;;;;;;kDAGpB,uMAAC;wCAAa,WAAU;;0DACtB,uMAAC;0DACC,cAAA,uMAAC;8DACC,cAAA,uMAAC;wDAAE,MAAM,KAAK,GAAG;wDAAE,WAAU;kEAC3B,cAAA,uMAAC;4DACC,KAAK,KAAK,GAAG;4DACb,WAAU;4DACV,KAAK,KAAK,GAAG;;;;;;;;;;;;;;;;;;;;;0DAKrB,uMAAC;gDAAI,WAAU;;kEACb,uMAAC;wDACC,MAAK;wDACL,WAAW;wDACX,WAAU;kEAET,KAAK,GAAG,CAAC,CAAC,OAAS,qBAAqB;;;;;;kEAG3C,uMAAC;wDAAI,WAAU;;0EACb,uMAAC;gEAAO,OAAO;gEAAC,SAAQ;0EACtB,cAAA,uMAAC;oEAAE,MAAM,KAAK,KAAK,CAAC,GAAG;8EAAG,KAAK,KAAK,CAAC,KAAK;;;;;;;;;;;0EAE5C,uMAAC;gEAAO,OAAO;0EACb,cAAA,uMAAC;oEAAE,MAAM,KAAK,MAAM,CAAC,GAAG;8EAAG,KAAK,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWlE;KA7KM;AA+KN,MAAM,iBAAiB,CAAC;IACtB,IAAI,KAAK,KAAK,EAAE;QACd,qBACE,uMAAC;;8BACC,uMAAC;8BAAuB,KAAK,KAAK;;;;;;8BAClC,uMAAC;oBAAsB,WAAU;8BAC9B,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,wBACf,uMAAC;4BAAmB,OAAO;4BAAqB,WAAU;sCACxD,cAAA,uMAAC;gCAAY,MAAM;;;;;;2BADY,QAAQ,KAAK;;;;;;;;;;;WAJ3B,KAAK,KAAK;;;;;IAWvC;IAEA,qBACE,uMAAC;kBACC,cAAA,uMAAC;YACC,MAAM,KAAK,GAAG;YACd,WAAU;sBAET,KAAK,KAAK;;;;;;OALU,KAAK,KAAK;;;;;AASvC;AAEA,MAAM,uBAAuB,CAAC;IAC5B,IAAI,KAAK,KAAK,EAAE;QACd,qBACE,uMAAC;YAA+B,OAAO,KAAK,KAAK;YAAE,WAAU;;8BAC3D,uMAAC;oBAAiB,WAAU;8BACzB,KAAK,KAAK;;;;;;8BAEb,uMAAC;oBAAiB,WAAU;8BACzB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,wBACf,uMAAC;4BAAgC,MAAM;2BAArB,QAAQ,KAAK;;;;;;;;;;;WANjB,KAAK,KAAK;;;;;IAWlC;IAEA,qBACE,uMAAC;QAAmB,MAAM,KAAK,GAAG;QAAE,WAAU;kBAC3C,KAAK,KAAK;OADL,KAAK,KAAK;;;;;AAItB;AAEA,MAAM,cAAc;QAAC,EAAE,IAAI,EAAsB;IAC/C,qBACE,uMAAC;QACC,WAAU;QACV,MAAM,KAAK,GAAG;;0BAEd,uMAAC;gBAAI,WAAU;0BAAmB,KAAK,IAAI;;;;;;0BAC3C,uMAAC;;kCACC,uMAAC;wBAAI,WAAU;kCAAyB,KAAK,KAAK;;;;;;oBACjD,KAAK,WAAW,kBACf,uMAAC;wBAAE,WAAU;kCACV,KAAK,WAAW;;;;;;;;;;;;;;;;;;AAM7B;MAjBM", "debugId": null}}]}